<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../../config/database.php';
include_once '../../models/Tweet.php';

// Get authorization header
$headers = apache_request_headers();
$token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';

if(empty($token)) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Token required."));
    exit();
}

// Decode token (simple base64 decode - in production use JWT)
$decoded_token = json_decode(base64_decode($token), true);

if(!$decoded_token || !isset($decoded_token['user_id']) || $decoded_token['exp'] < time()) {
    http_response_code(401);
    echo json_encode(array("message" => "Access denied. Invalid or expired token."));
    exit();
}

$database = new Database();
$db = $database->getConnection();

$tweet = new Tweet($db);

$data = json_decode(file_get_contents("php://input"));

if(!empty($data->content)) {
    $tweet->user_id = $decoded_token['user_id'];
    $tweet->content = $data->content;
    $tweet->image_url = isset($data->image_url) ? $data->image_url : null;
    $tweet->reply_to_tweet_id = isset($data->reply_to_tweet_id) ? $data->reply_to_tweet_id : null;
    $tweet->retweet_of_tweet_id = isset($data->retweet_of_tweet_id) ? $data->retweet_of_tweet_id : null;

    if($tweet->create()) {
        http_response_code(201);
        echo json_encode(array(
            "message" => "Tweet was created successfully.",
            "tweet_id" => $tweet->id
        ));
    } else {
        http_response_code(503);
        echo json_encode(array("message" => "Unable to create tweet."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("message" => "Unable to create tweet. Content is required."));
}
?>
