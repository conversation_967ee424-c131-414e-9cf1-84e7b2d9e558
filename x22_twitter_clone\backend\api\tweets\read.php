<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../../config/database.php';
include_once '../../models/Tweet.php';

$database = new Database();
$db = $database->getConnection();

$tweet = new Tweet($db);

// Get query parameters
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
$offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;

// Read tweets
if($user_id) {
    $stmt = $tweet->readByUser($user_id, $limit, $offset);
} else {
    $stmt = $tweet->read($limit, $offset);
}

$num = $stmt->rowCount();

if($num > 0) {
    $tweets_arr = array();
    $tweets_arr["records"] = array();

    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        extract($row);

        $tweet_item = array(
            "id" => $id,
            "user_id" => $user_id,
            "content" => $content,
            "image_url" => $image_url,
            "reply_to_tweet_id" => $reply_to_tweet_id,
            "retweet_of_tweet_id" => $retweet_of_tweet_id,
            "likes_count" => $likes_count,
            "retweets_count" => $retweets_count,
            "replies_count" => $replies_count,
            "created_at" => $created_at,
            "user" => array(
                "username" => $username,
                "display_name" => $display_name,
                "profile_image_url" => $profile_image_url,
                "verified" => $verified
            )
        );

        array_push($tweets_arr["records"], $tweet_item);
    }

    http_response_code(200);
    echo json_encode($tweets_arr);
} else {
    http_response_code(404);
    echo json_encode(array("message" => "No tweets found."));
}
?>
